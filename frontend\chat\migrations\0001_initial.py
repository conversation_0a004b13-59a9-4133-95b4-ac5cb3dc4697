# Generated by Django 4.2.7 on 2025-08-01 19:58

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True, verbose_name='معرف الجلسة')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('query_count', models.IntegerField(default=0, verbose_name='عدد الاستعلامات')),
            ],
            options={
                'verbose_name': 'جلسة محادثة',
                'verbose_name_plural': 'جلسات المحادثة',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='QueryHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query_text', models.TextField(verbose_name='نص الاستعلام')),
                ('language', models.CharField(choices=[('ar', 'عربي'), ('en', 'English')], default='ar', max_length=2)),
                ('sql_query', models.TextField(verbose_name='استعلام SQL')),
                ('sql_explanation', models.TextField(verbose_name='شرح الاستعلام')),
                ('confidence', models.FloatField(verbose_name='مستوى الثقة')),
                ('result_count', models.IntegerField(verbose_name='عدد النتائج')),
                ('execution_time', models.FloatField(verbose_name='وقت التنفيذ')),
                ('chart_type', models.CharField(max_length=20, verbose_name='نوع الرسم البياني')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='queries', to='chat.chatsession')),
            ],
            options={
                'verbose_name': 'تاريخ الاستعلام',
                'verbose_name_plural': 'تاريخ الاستعلامات',
                'ordering': ['-created_at'],
            },
        ),
    ]
