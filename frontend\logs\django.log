Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
Internal Server Error: /chat/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: chat_chatsession

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\projact\agentpy\frontend\chat\views.py", line 21, in index
    ChatSession.objects.get_or_create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        session_id=session_id,
        ^^^^^^^^^^^^^^^^^^^^^^
        defaults={'query_count': 0}
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 916, in get_or_create
    return self.get(**kwargs), False
           ~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: chat_chatsession
"GET /chat/ HTTP/1.1" 500 143850
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2445
